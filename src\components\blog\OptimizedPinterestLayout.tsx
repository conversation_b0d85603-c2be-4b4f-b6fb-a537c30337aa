'use client';

import { motion, useInView } from "framer-motion";
import { useState, useEffect, useRef } from "react";
import { OptimizedBlogCard } from "./OptimizedBlogCard";

interface BlogPost {
  id?: string;
  _id?: string;
  title: string;
  excerpt?: string;
  description?: string;
  slug: string;
  featuredImage?: string;
  image?: string;
  categoryName?: string;
  category?: string;
  publishedAt?: string;
  createdAt?: string;
  author: {
    name: string;
    email?: string;
  } | string;
  credit?: string;
}

interface OptimizedPinterestLayoutProps {
  posts: BlogPost[];
  loading?: boolean;
  showAnimation?: boolean;
  className?: string;
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

const itemVariants = {
  hidden: { 
    opacity: 0, 
    y: 30, 
    scale: 0.95 
  },
  visible: { 
    opacity: 1, 
    y: 0, 
    scale: 1,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
};

export function OptimizedPinterestLayout({ 
  posts, 
  loading = false, 
  showAnimation = true,
  className = ""
}: OptimizedPinterestLayoutProps) {
  const [columns, setColumns] = useState(3);
  const [mounted, setMounted] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(containerRef, { once: true, margin: "-100px" });

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    const updateColumns = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setColumns(1); // Mobile: 1 column
      } else if (width < 1024) {
        setColumns(2); // Tablet: 2 columns
      } else if (width < 1280) {
        setColumns(3); // Desktop: 3 columns
      } else {
        setColumns(4); // Large desktop: 4 columns
      }
    };

    updateColumns();
    window.addEventListener('resize', updateColumns);
    return () => window.removeEventListener('resize', updateColumns);
  }, []);

  if (!mounted) {
    return <div className="h-96 animate-pulse bg-muted rounded-lg" />;
  }

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.from({ length: 9 }).map((_, index) => (
          <div key={index} className="space-y-4">
            <div className="h-52 bg-muted animate-pulse rounded-t-2xl" />
            <div className="p-6 space-y-3">
              <div className="h-4 bg-muted animate-pulse rounded" />
              <div className="h-6 bg-muted animate-pulse rounded" />
              <div className="h-4 bg-muted animate-pulse rounded w-3/4" />
              <div className="h-4 bg-muted animate-pulse rounded w-1/2" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!posts || posts.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center py-20"
      >
        <div className="text-6xl mb-4">📝</div>
        <h3 className="text-xl font-medium mb-2 text-foreground">
          No articles found
        </h3>
        <p className="text-muted-foreground">
          Check back later for new content!
        </p>
      </motion.div>
    );
  }

  // CSS Columns approach for better Pinterest-style distribution
  return (
    <motion.div
      ref={containerRef}
      variants={showAnimation && isInView ? containerVariants : undefined}
      initial={showAnimation ? "hidden" : false}
      animate={showAnimation && isInView ? "visible" : false}
      className={`w-full ${className}`}
    >
      <div
        className="pinterest-masonry-optimized"
        style={{
          columns: `${columns}`,
          columnGap: columns === 1 ? '1rem' : columns === 2 ? '1.5rem' : '2rem',
          columnFill: 'balance',
          orphans: 1,
          widows: 1,
        }}
      >
        {posts.map((post, index) => (
          <motion.div
            key={post.id || post._id || index}
            variants={showAnimation && isInView ? itemVariants : undefined}
            className="break-inside-avoid inline-block w-full"
            style={{
              marginBottom: columns === 1 ? '1rem' : '1.5rem',
            }}
          >
            <OptimizedBlogCard
              post={post}
              index={index}
              showAnimation={false} // We handle animation at container level
              className="w-full"
            />
          </motion.div>
        ))}
      </div>

      {/* Custom CSS for better masonry behavior */}
      <style jsx>{`
        .pinterest-masonry-optimized {
          overflow: visible;
          height: auto;
        }
        
        .pinterest-masonry-optimized > div {
          display: inline-block;
          vertical-align: top;
          width: 100% !important;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
          .pinterest-masonry-optimized {
            columns: 1 !important;
            column-gap: 1rem !important;
          }
        }

        @media (min-width: 769px) and (max-width: 1024px) {
          .pinterest-masonry-optimized {
            columns: 2 !important;
            column-gap: 1.5rem !important;
          }
        }

        @media (min-width: 1025px) and (max-width: 1280px) {
          .pinterest-masonry-optimized {
            columns: 3 !important;
            column-gap: 2rem !important;
          }
        }

        @media (min-width: 1281px) {
          .pinterest-masonry-optimized {
            columns: 4 !important;
            column-gap: 2rem !important;
          }
        }
      `}</style>
    </motion.div>
  );
}
